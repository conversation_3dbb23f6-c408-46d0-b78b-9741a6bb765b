#include "sql/sql_index.h"
#include "sql/sql_row.h"
#include "sql/sql_view.h"
#include "sql/sql_table.h"
#include "sql/sql_database.h"
#include <iostream>

using namespace database;

int main() {
    try {
        std::cout << "=== Testing SqlIndex Implementation ===" << std::endl;
        
        // Test SqlIndex
        SqlIndex index("idx_user_email", SqlIndexType::Unique);
        index.setTableName("users");
        index.addColumn("email", SqlSortOrder::Ascending);
        index.setComment("Unique index on user email");
        
        std::cout << "Index name: " << index.name() << std::endl;
        std::cout << "Index type: " << static_cast<int>(index.indexType()) << std::endl;
        std::cout << "Table name: " << index.tableName() << std::endl;
        std::cout << "Is unique: " << index.isUnique() << std::endl;
        std::cout << "CREATE SQL: " << index.createSql() << std::endl;
        
        std::cout << "\n=== Testing SqlRow Implementation ===" << std::endl;
        
        // Test SqlRow
        SqlRow row("users");
        row.setValue("id", Data(1));
        row.setValue("name", Data("John Doe"));
        row.setValue("email", Data("<EMAIL>"));
        row.setValue("age", Data(30));
        
        std::cout << "Row table: " << row.tableName() << std::endl;
        std::cout << "Column count: " << row.columnCount() << std::endl;
        std::cout << "User name: " << row.value("name").to<std::string>() << std::endl;
        std::cout << "User email: " << row.value("email").to<std::string>() << std::endl;
        std::cout << "Is dirty: " << row.isDirty() << std::endl;
        std::cout << "INSERT SQL: " << row.toInsertStatement() << std::endl;
        std::cout << "JSON: " << row.toJson() << std::endl;
        
        std::cout << "\n=== Testing SqlView Implementation ===" << std::endl;
        
        // Test SqlView
        std::string viewDef = "SELECT id, name, email FROM users WHERE active = 1";
        SqlView view("active_users", viewDef);
        view.setSchema("public");
        view.setComment("View of active users only");
        view.setUpdatable(true);
        view.setColumnNames({"id", "name", "email"});
        
        std::cout << "View name: " << view.name() << std::endl;
        std::cout << "View schema: " << view.schema() << std::endl;
        std::cout << "View definition: " << view.definition() << std::endl;
        std::cout << "Is updatable: " << view.isUpdatable() << std::endl;
        std::cout << "Qualified name: " << view.qualifiedName() << std::endl;
        std::cout << "CREATE SQL: " << view.createStatement() << std::endl;
        
        std::cout << "\n=== Testing Integration ===" << std::endl;
        
        // Test integration between components
        SqlTable table("users");
        
        // Create index associated with table
        SqlIndex tableIndex("idx_users_name", table);
        tableIndex.addColumn("name");
        tableIndex.setIndexType(SqlIndexType::Normal);
        
        std::cout << "Table index name: " << tableIndex.name() << std::endl;
        std::cout << "Table index table: " << tableIndex.tableName() << std::endl;
        
        // Create row for the table
        SqlRow tableRow("users");
        tableRow.setTable(table);
        tableRow.setValue("id", Data(2));
        tableRow.setValue("name", Data("Jane Smith"));
        tableRow.setValue("email", Data("<EMAIL>"));
        
        auto associatedTable = tableRow.table();
        std::cout << "Row's associated table: " << associatedTable.name() << std::endl;
        
        // Test factory methods
        SqlView materializedView = SqlView::createMaterialized(
            "user_stats",
            "SELECT COUNT(*) as total, AVG(age) as avg_age FROM users"
        );
        
        std::cout << "Materialized view: " << materializedView.name() << std::endl;
        std::cout << "Is materialized: " << materializedView.isMaterialized() << std::endl;
        
        SqlView updatableView = SqlView::createUpdatable(
            "user_details",
            "SELECT id, name, email FROM users"
        );
        
        std::cout << "Updatable view: " << updatableView.name() << std::endl;
        std::cout << "Is updatable: " << updatableView.isUpdatable() << std::endl;
        
        std::cout << "\n=== Testing Copy/Move Operations ===" << std::endl;
        
        // Test copy operations
        SqlIndex indexCopy = index;
        SqlRow rowCopy = row;
        SqlView viewCopy = view;
        
        std::cout << "Copied index name: " << indexCopy.name() << std::endl;
        std::cout << "Copied row table: " << rowCopy.tableName() << std::endl;
        std::cout << "Copied view name: " << viewCopy.name() << std::endl;
        
        // Test move operations
        SqlIndex indexMoved = std::move(indexCopy);
        SqlRow rowMoved = std::move(rowCopy);
        SqlView viewMoved = std::move(viewCopy);
        
        std::cout << "Moved index name: " << indexMoved.name() << std::endl;
        std::cout << "Moved row table: " << rowMoved.tableName() << std::endl;
        std::cout << "Moved view name: " << viewMoved.name() << std::endl;
        
        std::cout << "\n=== Testing Metadata Operations ===" << std::endl;
        
        // Test metadata access
        auto indexMetadata = index.metadata();
        std::cout << "Index metadata table: " << indexMetadata.tableName << std::endl;
        std::cout << "Index metadata columns: " << indexMetadata.columns.size() << std::endl;
        
        auto rowMetadata = row.metadata();
        std::cout << "Row metadata table: " << rowMetadata->tableName << std::endl;
        std::cout << "Row metadata dirty: " << rowMetadata->isDirty << std::endl;
        
        auto viewMetadata = view.metadata();
        std::cout << "View metadata schema: " << viewMetadata->schema << std::endl;
        std::cout << "View metadata updatable: " << viewMetadata->isUpdatable << std::endl;
        
        std::cout << "\n=== Testing Validation ===" << std::endl;
        
        // Test validation
        std::cout << "Row is valid: " << row.isValid() << std::endl;
        std::cout << "View definition is valid: " << view.isDefinitionValid() << std::endl;
        
        auto validationErrors = row.validationErrors();
        std::cout << "Row validation errors: " << validationErrors.size() << std::endl;
        
        std::cout << "\n=== All Tests Completed Successfully! ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
