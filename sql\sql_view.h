#ifndef DATABASE_SQL_VIEW_H
#define DATABASE_SQL_VIEW_H

#include <memory>
#include <vector>
#include <optional>
#include <chrono>
#include <unordered_map>

#include "sql_object.h"
#include "sql_types.h"

namespace database {

// Forward declarations
class SqlViewPrivate;
class SqlTable;
class SqlColumn;
class SqlRow;
class SqlDatabase;

/**
 * @brief SQL view object with three-tier architecture
 * 
 * This class represents a database view and implements the three-tier architecture:
 * - Tier 1 (Core Concepts): Lightweight base with essential properties (inherited from SqlObject)
 * - Tier 2 (Extended Metadata): Optional comprehensive metadata (SqlViewMetadata)
 * - Tier 3 (Database Interaction): Actual database operations via PIMPL (SqlViewPrivate)
 * 
 * The three-tier implementation is hidden from the public API to provide clean, intuitive interfaces.
 */
class SqlView final : public SqlObject {
public:

    /**
     * @brief Metadata for SQL view objects (Tier 2 - Extended Metadata)
     *
     * This struct contains comprehensive descriptive information about database views.
     */
    struct SqlViewMetadata {
        std::string schema;                                 ///< Schema name
        std::string definition;                             ///< View definition (SELECT statement)
        std::string comment;                                ///< View comment/description
        std::vector<std::string> referencedTables;         ///< Tables referenced by the view
        std::vector<std::string> columns;                   ///< View columns
        bool isUpdatable = false;                           ///< Is updatable view
        bool isMaterialized = false;                        ///< Is materialized view
        bool withCheckOption = false;                       ///< Has WITH CHECK OPTION
        std::string checkOption;                            ///< Check option type (LOCAL/CASCADED)
        std::string securityType;                           ///< Security type (DEFINER/INVOKER)
        std::string definer;                                ///< View definer
        std::optional<std::chrono::system_clock::time_point> createdTime; ///< Creation time
        std::optional<std::chrono::system_clock::time_point> lastRefreshTime; ///< Last refresh time (for materialized views)
        std::unordered_map<std::string, std::string> properties; ///< Additional properties

    /**
     * @brief Default constructor
     */
        SqlViewMetadata() = default;

        /**
     * @brief Constructor with basic information
     */
        SqlViewMetadata(std::string schema, std::string definition, std::string comment = {})
            : schema(std::move(schema)), definition(std::move(definition)), comment(std::move(comment)) {}
    };


    /**
     * @brief Default constructor
     * 
     * Creates an empty view object (Tier 1 only).
     */
    SqlView();

    /**
     * @brief Constructor with view name
     * @param name The view name
     */
    explicit SqlView(std::string name);

    /**
     * @brief Constructor with view name and definition
     * @param name The view name
     * @param definition The view definition (SELECT statement)
     */
    SqlView(std::string name, std::string definition);

    /**
     * @brief Constructor with view name and metadata
     * @param name The view name
     * @param metadata View metadata (Tier 2)
     */
    SqlView(std::string name, SqlViewMetadata metadata);

    /**
     * @brief Constructor with view name, metadata, and database connection
     * @param name The view name
     * @param metadata View metadata (Tier 2)
     * @param database Database connection for operations (Tier 3)
     */
    SqlView(std::string name, SqlViewMetadata metadata, std::shared_ptr<SqlDatabase> database);

    /**
     * @brief Destructor
     */
    ~SqlView() override;

    /**
     * @brief Copy constructor
     */
    SqlView(const SqlView& other);

    /**
     * @brief Move constructor
     */
    SqlView(SqlView&& other) noexcept;

    /**
     * @brief Copy assignment operator
     */
    SqlView& operator=(const SqlView& other);

    /**
     * @brief Move assignment operator
     */
    SqlView& operator=(SqlView&& other) noexcept;

    // Tier 2 - Extended Metadata Operations

    /**
     * @brief Check if extended metadata is available
     * @return True if metadata is loaded, false otherwise
     */
    [[nodiscard]] bool hasMetadata() const noexcept;

    /**
     * @brief Get view metadata (loads Tier 2 if not already loaded)
     * @return Shared pointer to view metadata
     */
    [[nodiscard]] std::shared_ptr<SqlViewMetadata> metadata() const;

    /**
     * @brief Set view metadata
     * @param metadata New view metadata
     */
    void setMetadata(SqlViewMetadata metadata);

    /**
     * @brief Set view metadata (shared)
     * @param metadata Shared pointer to view metadata
     */
    void setMetadata(std::shared_ptr<SqlViewMetadata> metadata);

    /**
     * @brief Get schema name from metadata
     * @return Schema name, or empty string if no metadata
     */
    [[nodiscard]] std::string schema() const;

    /**
     * @brief Set schema name in metadata
     * @param schema Schema name
     */
    void setSchema(std::string schema);

    /**
     * @brief Get view definition from metadata
     * @return View definition (SELECT statement), or empty string if no metadata
     */
    [[nodiscard]] std::string definition() const;

    /**
     * @brief Set view definition in metadata
     * @param definition View definition (SELECT statement)
     */
    void setDefinition(std::string definition);

    /**
     * @brief Get view comment from metadata
     * @return View comment, or empty string if no metadata
     */
    [[nodiscard]] std::string comment() const;

    /**
     * @brief Set view comment in metadata
     * @param comment View comment
     */
    void setComment(std::string comment);

    /**
     * @brief Get referenced tables from metadata
     * @return Vector of table names referenced by the view
     */
    [[nodiscard]] std::vector<std::string> referencedTables() const;

    /**
     * @brief Set referenced tables in metadata
     * @param tables Vector of table names referenced by the view
     */
    void setReferencedTables(std::vector<std::string> tables);

    /**
     * @brief Get view columns from metadata
     * @return Vector of column names
     */
    [[nodiscard]] std::vector<std::string> columnNames() const;

    /**
     * @brief Set view columns in metadata
     * @param columns Vector of column names
     */
    void setColumnNames(std::vector<std::string> columns);

    /**
     * @brief Check if view is updatable
     * @return True if updatable, false otherwise
     */
    [[nodiscard]] bool isUpdatable() const;

    /**
     * @brief Set updatable flag in metadata
     * @param updatable True for updatable view
     */
    void setUpdatable(bool updatable);

    /**
     * @brief Check if view is materialized
     * @return True if materialized, false otherwise
     */
    [[nodiscard]] bool isMaterialized() const;

    /**
     * @brief Set materialized flag in metadata
     * @param materialized True for materialized view
     */
    void setMaterialized(bool materialized);

    /**
     * @brief Check if view has WITH CHECK OPTION
     * @return True if has check option, false otherwise
     */
    [[nodiscard]] bool hasCheckOption() const;

    /**
     * @brief Set WITH CHECK OPTION flag in metadata
     * @param withCheckOption True for WITH CHECK OPTION
     */
    void setCheckOption(bool withCheckOption);

    /**
     * @brief Get check option type from metadata
     * @return Check option type (LOCAL/CASCADED), or empty string if none
     */
    [[nodiscard]] std::string checkOptionType() const;

    /**
     * @brief Set check option type in metadata
     * @param checkOption Check option type (LOCAL/CASCADED)
     */
    void setCheckOptionType(std::string checkOption);

    /**
     * @brief Get security type from metadata
     * @return Security type (DEFINER/INVOKER), or empty string if not set
     */
    [[nodiscard]] std::string securityType() const;

    /**
     * @brief Set security type in metadata
     * @param securityType Security type (DEFINER/INVOKER)
     */
    void setSecurityType(std::string securityType);

    /**
     * @brief Get view definer from metadata
     * @return View definer, or empty string if not set
     */
    [[nodiscard]] std::string definer() const;

    /**
     * @brief Set view definer in metadata
     * @param definer View definer
     */
    void setDefiner(std::string definer);

    // Tier 3 - Database Interaction Operations

    /**
     * @brief Check if database connection is available for operations
     * @return True if connected to database, false otherwise
     */
    [[nodiscard]] bool isConnected() const noexcept;

    /**
     * @brief Get database connection
     * @return Shared pointer to database connection
     */
    [[nodiscard]] std::shared_ptr<SqlDatabase> database() const;

    /**
     * @brief Set database connection (loads Tier 3 if not already loaded)
     * @param database Database connection
     */
    void setDatabase(std::shared_ptr<SqlDatabase> database);

    /**
     * @brief Check if view exists in the database
     * @return True if exists, false otherwise or if not connected
     */
    [[nodiscard]] bool exists() const;

    /**
     * @brief Create view in the database
     * @param orReplace Add OR REPLACE clause
     * @return True if successful, false otherwise
     */
    bool create(bool orReplace = false);

    /**
     * @brief Drop view from the database
     * @param ifExists Add IF EXISTS clause
     * @return True if successful, false otherwise
     */
    bool drop(bool ifExists = true);

    /**
     * @brief Rename view in the database
     * @param newName New view name
     * @return True if successful, false otherwise
     */
    bool rename(const std::string& newName);

    /**
     * @brief Refresh materialized view (if applicable)
     * @return True if successful, false otherwise or if not materialized
     */
    bool refresh();

    /**
     * @brief Get list of columns from database
     * @return Vector of column objects
     */
    [[nodiscard]] std::vector<SqlColumn> columns() const;

    /**
     * @brief Get column by name from database
     * @param columnName Column name
     * @return Column object, or invalid column if not found
     */
    [[nodiscard]] SqlColumn column(const std::string& columnName) const;

    /**
     * @brief Get row count from view
     * @return Number of rows, or 0 if error or not connected
     */
    [[nodiscard]] size_t rowCount() const;

    /**
     * @brief Select all rows from view
     * @return Vector of row objects
     */
    [[nodiscard]] std::vector<SqlRow> selectAll() const;

    /**
     * @brief Select rows with condition
     * @param whereClause WHERE clause condition
     * @param parameters Parameter values for prepared statement
     * @return Vector of row objects
     */
    [[nodiscard]] std::vector<SqlRow> select(const std::string& whereClause, 
                                            const std::vector<Data>& parameters = {}) const;

    /**
     * @brief Select rows with limit and offset
     * @param limit Maximum number of rows
     * @param offset Number of rows to skip
     * @return Vector of row objects
     */
    [[nodiscard]] std::vector<SqlRow> select(size_t limit, size_t offset = 0) const;

    /**
     * @brief Insert row into view (if updatable)
     * @param row Row to insert
     * @return True if successful, false otherwise
     */
    bool insert(const SqlRow& row);

    /**
     * @brief Update rows in view (if updatable)
     * @param row Row with updated values
     * @param whereClause WHERE clause condition
     * @param parameters Parameter values for prepared statement
     * @return Number of affected rows
     */
    size_t update(const SqlRow& row, const std::string& whereClause, 
                  const std::vector<Data>& parameters = {});

    /**
     * @brief Delete rows from view (if updatable)
     * @param whereClause WHERE clause condition
     * @param parameters Parameter values for prepared statement
     * @return Number of affected rows
     */
    size_t deleteRows(const std::string& whereClause, 
                     const std::vector<Data>& parameters = {});

    /**
     * @brief Analyze view dependencies
     * @return List of dependent objects
     */
    [[nodiscard]] std::vector<std::string> dependencies() const;

    /**
     * @brief Check if view definition is valid
     * @return True if valid, false otherwise
     */
    [[nodiscard]] bool isDefinitionValid() const;

    /**
     * @brief Refresh metadata from database
     * @return True if successful, false otherwise
     */
    bool refreshMetadata();

    /**
     * @brief Execute custom SQL statement on this view
     * @param sql SQL statement
     * @param parameters Parameter values for prepared statement
     * @return True if successful, false otherwise
     */
    bool execute(const std::string& sql, const std::vector<Data>& parameters = {});

    // Utility methods

    /**
     * @brief Get qualified view name (schema.name)
     * @return Qualified view name
     */
    [[nodiscard]] std::string qualifiedName() const override;

    /**
     * @brief Get SQL CREATE VIEW statement
     * @return CREATE VIEW statement
     */
    [[nodiscard]] std::string createStatement() const;

    /**
     * @brief Get string representation of the view
     * @return String representation
     */
    [[nodiscard]] std::string toSql() const override;

    /**
     * @brief Create view from existing database view
     * @param database Database connection
     * @param viewName View name
     * @param loadMetadata Whether to load metadata immediately
     * @return View object
     */
    [[nodiscard]] static SqlView fromDatabase(std::shared_ptr<SqlDatabase> database, 
                                             const std::string& viewName, 
                                             bool loadMetadata = true);

    /**
     * @brief Create view with definition
     * @param name View name
     * @param definition View definition (SELECT statement)
     * @param schema Schema name (optional)
     * @return View object
     */
    [[nodiscard]] static SqlView withDefinition(std::string name, 
                                               std::string definition, 
                                               std::string schema = {});

    /**
     * @brief Create materialized view
     * @param name View name
     * @param definition View definition (SELECT statement)
     * @param schema Schema name (optional)
     * @return Materialized view object
     */
    [[nodiscard]] static SqlView materialized(std::string name, 
                                             std::string definition, 
                                             std::string schema = {});

    /**
     * @brief Create updatable view with check option
     * @param name View name
     * @param definition View definition (SELECT statement)
     * @param checkOption Check option type (LOCAL/CASCADED)
     * @param schema Schema name (optional)
     * @return Updatable view object
     */
    [[nodiscard]] static SqlView updatable(std::string name, 
                                          std::string definition, 
                                          std::string checkOption = "LOCAL", 
                                          std::string schema = {});

private:
    mutable std::shared_ptr<SqlViewMetadata> m_metadata;
    std::shared_ptr<SqlViewPrivate> d_ptr;

    // Helper methods for tier management
    void ensureDatabase() const;
};

} // namespace database

#endif // DATABASE_SQL_VIEW_H 
