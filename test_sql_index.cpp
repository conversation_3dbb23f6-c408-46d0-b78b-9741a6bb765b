#include "sql/sql_index.h"
#include "sql/sql_table.h"
#include "sql/sql_database.h"
#include <iostream>

using namespace database;

int main() {
    try {
        // Test basic index creation
        SqlIndex index1("idx_user_email", SqlIndexType::Unique);
        std::cout << "Created index: " << index1.name() << std::endl;
        std::cout << "Index type: " << static_cast<int>(index1.indexType()) << std::endl;
        
        // Test metadata
        index1.setTableName("users");
        index1.setComment("Index on user email for fast lookups");
        index1.addColumn("email", SqlSortOrder::Ascending);
        index1.setUnique(true);
        
        std::cout << "Table name: " << index1.tableName() << std::endl;
        std::cout << "Comment: " << index1.comment() << std::endl;
        std::cout << "Is unique: " << index1.isUnique() << std::endl;
        std::cout << "Is primary key: " << index1.isPrimaryKey() << std::endl;
        
        // Test column management
        auto columns = index1.columns();
        std::cout << "Column count: " << columns.size() << std::endl;
        if (!columns.empty()) {
            std::cout << "First column: " << columns[0] << std::endl;
        }
        
        // Test adding multiple columns
        index1.addColumn("created_at", SqlSortOrder::Descending);
        columns = index1.columns();
        std::cout << "Column count after adding: " << columns.size() << std::endl;
        
        // Test sort orders
        auto sortOrders = index1.sortOrders();
        std::cout << "Sort orders count: " << sortOrders.size() << std::endl;
        
        // Test SQL generation
        std::cout << "Qualified name: " << index1.qualifiedName() << std::endl;
        std::cout << "CREATE SQL: " << index1.createSql(true) << std::endl;
        std::cout << "DROP SQL: " << index1.dropSql(true) << std::endl;
        
        // Test with table association
        SqlTable table("users");
        SqlIndex index2("idx_user_name", table);
        index2.setIndexType(SqlIndexType::Normal);
        index2.addColumn("first_name");
        index2.addColumn("last_name");
        
        std::cout << "Index 2 table name: " << index2.tableName() << std::endl;
        std::cout << "Index 2 CREATE SQL: " << index2.createSql() << std::endl;
        
        // Test primary key index
        SqlIndex primaryIndex("PRIMARY", SqlIndexType::Primary);
        primaryIndex.setTableName("users");
        primaryIndex.addColumn("id");
        
        std::cout << "Primary key CREATE SQL: " << primaryIndex.createSql() << std::endl;
        std::cout << "Primary key DROP SQL: " << primaryIndex.dropSql() << std::endl;
        
        // Test partial index with WHERE clause
        SqlIndex partialIndex("idx_active_users", SqlIndexType::Normal);
        partialIndex.setTableName("users");
        partialIndex.addColumn("status");
        partialIndex.setWhereClause("status = 'active'");
        
        std::cout << "Partial index CREATE SQL: " << partialIndex.createSql() << std::endl;
        
        // Test fill factor
        SqlIndex clusteredIndex("idx_clustered", SqlIndexType::Clustered);
        clusteredIndex.setTableName("users");
        clusteredIndex.addColumn("id");
        clusteredIndex.setFillFactor(80);
        clusteredIndex.setClustered(true);
        
        std::cout << "Clustered index: " << clusteredIndex.isClustered() << std::endl;
        std::cout << "Fill factor: " << clusteredIndex.fillFactor().value_or(0) << std::endl;
        std::cout << "Clustered CREATE SQL: " << clusteredIndex.createSql() << std::endl;
        
        // Test column removal
        index1.removeColumn("created_at");
        columns = index1.columns();
        std::cout << "Column count after removal: " << columns.size() << std::endl;
        
        // Test clear columns
        index1.clearColumns();
        columns = index1.columns();
        std::cout << "Column count after clear: " << columns.size() << std::endl;
        
        // Test copy constructor
        SqlIndex index3 = index2;
        std::cout << "Copied index name: " << index3.name() << std::endl;
        std::cout << "Copied index table: " << index3.tableName() << std::endl;
        
        // Test visibility and system flags
        index1.setVisible(false);
        index1.setSystem(true);
        std::cout << "Is visible: " << index1.isVisible() << std::endl;
        std::cout << "Is system: " << index1.isSystem() << std::endl;
        
        std::cout << "All SqlIndex tests passed!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
