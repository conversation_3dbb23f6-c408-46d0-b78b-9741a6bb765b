#include "sql/sql_table.h"
#include "sql/sql_column.h"
#include "sql/sql_database.h"
#include <iostream>

using namespace database;

int main() {
    try {
        // Test basic table creation
        SqlTable table1("users");
        std::cout << "Created table: " << table1.name() << std::endl;
        
        // Test metadata
        table1.setSchema("public");
        table1.setEngine("InnoDB");
        table1.setComment("User accounts table");
        table1.setCharset("utf8mb4");
        table1.setCollation("utf8mb4_unicode_ci");
        
        std::cout << "Schema: " << table1.schema() << std::endl;
        std::cout << "Engine: " << table1.engine() << std::endl;
        std::cout << "Comment: " << table1.comment() << std::endl;
        std::cout << "Charset: " << table1.charset() << std::endl;
        std::cout << "Collation: " << table1.collation() << std::endl;
        
        // Test column management
        SqlColumn idColumn("id", SqlDataType::Integer);
        idColumn.setPrimaryKey(true);
        idColumn.setAutoIncrement(true);
        idColumn.setNullable(false);
        
        SqlColumn nameColumn("name", SqlDataType::Varchar);
        nameColumn.setMaxLength(255);
        nameColumn.setNullable(false);
        
        SqlColumn emailColumn("email", SqlDataType::Varchar);
        emailColumn.setMaxLength(255);
        emailColumn.setUnique(true);
        
        table1.addColumn(idColumn);
        table1.addColumn(nameColumn);
        table1.addColumn(emailColumn);
        
        std::cout << "Column count: " << table1.columnCount() << std::endl;
        std::cout << "Has 'name' column: " << table1.hasColumn("name") << std::endl;
        std::cout << "Has 'age' column: " << table1.hasColumn("age") << std::endl;
        
        // Test SQL generation
        std::cout << "Qualified name: " << table1.qualifiedName() << std::endl;
        std::cout << "CREATE SQL: " << table1.createSql(true) << std::endl;
        std::cout << "DROP SQL: " << table1.dropSql(true) << std::endl;
        
        // Test column retrieval
        auto retrievedColumn = table1.column("name");
        std::cout << "Retrieved column name: " << retrievedColumn.name() << std::endl;
        std::cout << "Retrieved column data type: " << static_cast<int>(retrievedColumn.dataType()) << std::endl;
        
        // Test index management
        std::vector<std::string> indexColumns = {"email"};
        bool indexAdded = table1.addIndex("idx_email", indexColumns, true);
        std::cout << "Index added: " << indexAdded << std::endl;
        std::cout << "Has index 'idx_email': " << table1.hasIndex("idx_email") << std::endl;
        
        // Test copy constructor
        SqlTable table2 = table1;
        std::cout << "Copied table name: " << table2.name() << std::endl;
        std::cout << "Copied table column count: " << table2.columnCount() << std::endl;
        
        // Test move constructor
        SqlTable table3 = std::move(table2);
        std::cout << "Moved table name: " << table3.name() << std::endl;
        std::cout << "Moved table column count: " << table3.columnCount() << std::endl;
        
        // Test with schema and alias
        SqlTable table4("products", "inventory", "p");
        std::cout << "Table with schema: " << table4.qualifiedName() << std::endl;
        
        std::cout << "All tests passed!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
