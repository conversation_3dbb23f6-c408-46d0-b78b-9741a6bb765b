#ifndef DATABASE_SQL_CONDITION_H
#define DATABASE_SQL_CONDITION_H

#include <string>
#include <string_view>
#include <vector>
#include <memory>

#include "sql_types.h"
#include "sql_enums.h"

namespace database {

/**
 * @brief Represents a SQL condition/predicate for WHERE clauses
 * 
 * This class encapsulates SQL conditions that can be used in WHERE clauses,
 * JOIN conditions, and other SQL predicates. It supports various comparison
 * operators and can be combined with logical operators.
 */
class SqlCondition {
public:
    /**
     * @brief Default constructor - creates an empty condition
     */
    SqlCondition() = default;
    
    /**
     * @brief Constructor for simple comparison conditions
     * @param columnName The column name
     * @param op The comparison operator
     * @param value The value to compare against
     */
    SqlCondition(std::string_view columnName, SqlConditionOperator op, const Variant& value);
    
    /**
     * @brief Constructor for conditions with multiple values (IN, NOT IN)
     * @param columnName The column name
     * @param op The comparison operator
     * @param values The values to compare against
     */
    SqlCondition(std::string_view columnName, SqlConditionOperator op, const std::vector<Variant>& values);
    
    /**
     * @brief Constructor for BETWEEN conditions
     * @param columnName The column name
     * @param minValue The minimum value
     * @param maxValue The maximum value
     */
    SqlCondition(std::string_view columnName, const Variant& minValue, const Variant& maxValue);
    
    /**
     * @brief Constructor for NULL checks
     * @param columnName The column name
     * @param op Must be IsNull or IsNotNull
     */
    SqlCondition(std::string_view columnName, SqlConditionOperator op);
    
    /**
     * @brief Copy constructor
     */
    SqlCondition(const SqlCondition& other) = default;
    
    /**
     * @brief Move constructor
     */
    SqlCondition(SqlCondition&& other) noexcept = default;
    
    /**
     * @brief Copy assignment operator
     */
    SqlCondition& operator=(const SqlCondition& other) = default;
    
    /**
     * @brief Move assignment operator
     */
    SqlCondition& operator=(SqlCondition&& other) noexcept = default;
    
    /**
     * @brief Destructor
     */
    ~SqlCondition() = default;
    
    /**
     * @brief Check if the condition is valid/non-empty
     * @return True if the condition is valid, false otherwise
     */
    [[nodiscard]] bool isValid() const noexcept;
    
    /**
     * @brief Get the column name
     * @return The column name
     */
    [[nodiscard]] const std::string& columnName() const noexcept;
    
    /**
     * @brief Get the operator
     * @return The comparison operator
     */
    [[nodiscard]] SqlConditionOperator operatorType() const noexcept;
    
    /**
     * @brief Get the single value (for simple comparisons)
     * @return The value, or empty Variant if not applicable
     */
    [[nodiscard]] const Variant& value() const noexcept;
    
    /**
     * @brief Get the values (for IN, NOT IN conditions)
     * @return The vector of values
     */
    [[nodiscard]] const std::vector<Variant>& values() const noexcept;
    
    /**
     * @brief Generate SQL string representation
     * @return The SQL condition string
     */
    [[nodiscard]] std::string toSql() const;
    
    /**
     * @brief Generate SQL string with parameter placeholders
     * @param parameters Output vector to store parameter values
     * @return The SQL condition string with placeholders
     */
    [[nodiscard]] std::string toSql(std::vector<Variant>& parameters) const;
    
    /**
     * @brief Combine with another condition using AND
     * @param other The other condition
     * @return A new combined condition
     */
    [[nodiscard]] SqlCondition operator&&(const SqlCondition& other) const;
    
    /**
     * @brief Combine with another condition using OR
     * @param other The other condition
     * @return A new combined condition
     */
    [[nodiscard]] SqlCondition operator||(const SqlCondition& other) const;
    
    /**
     * @brief Negate the condition using NOT
     * @return A new negated condition
     */
    [[nodiscard]] SqlCondition operator!() const;
    
    /**
     * @brief Static factory method for AND combination
     * @param left The left condition
     * @param right The right condition
     * @return A new combined condition
     */
    [[nodiscard]] static SqlCondition And(const SqlCondition& left, const SqlCondition& right);
    
    /**
     * @brief Static factory method for OR combination
     * @param left The left condition
     * @param right The right condition
     * @return A new combined condition
     */
    [[nodiscard]] static SqlCondition Or(const SqlCondition& left, const SqlCondition& right);
    
    /**
     * @brief Static factory method for NOT negation
     * @param condition The condition to negate
     * @return A new negated condition
     */
    [[nodiscard]] static SqlCondition Not(const SqlCondition& condition);

private:
    std::string m_columnName;
    SqlConditionOperator m_operator = SqlConditionOperator::Equal;
    Variant m_value;
    std::vector<Variant> m_values;
    SqlLogicalOperator m_logicalOperator = SqlLogicalOperator::And;
    std::shared_ptr<SqlCondition> m_leftCondition;
    std::shared_ptr<SqlCondition> m_rightCondition;
    bool m_isCompound = false;
    bool m_isNegated = false;
    
    // Helper methods
    std::string formatValue(const Variant& value) const;
    std::string formatValues(const std::vector<Variant>& values) const;
};

} // namespace database

#endif // DATABASE_SQL_CONDITION_H
