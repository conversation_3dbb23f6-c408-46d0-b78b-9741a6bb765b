#include "sql_table.h"
#include "sql_column.h"
#include "sql_database.h"
#include "sql_query.h"
#include "sql_index.h"
#include "driver/sql_record.h"
#include "exception/sql_error.h"

#include <sstream>
#include <algorithm>

namespace database {

// For now, use SqlRecord as SqlRow until SqlRow is properly defined
using SqlRow = SqlRecord;

/**
 * @brief Private implementation class for SqlTable
 *
 * This class provides the database interaction layer (Tier 3) for SqlTable.
 * It manages the actual database operations and maintains connection state.
 */
class SqlTablePrivate {
public:
    SqlTablePrivate() = default;
    ~SqlTablePrivate() = default;

    // Database connection
    std::shared_ptr<SqlDatabase> database;

    // Error information
    SqlError lastError;

    // State flags
    bool databaseEnabled = false;

    void setDatabase(std::shared_ptr<SqlDatabase> db) {
        database = db;
        databaseEnabled = (db != nullptr);
    }

    bool isValid() const {
        return databaseEnabled && database && database->isOpen();
    }

    void setError(const std::string& message, ErrorCode code = ErrorCode::Unknown) {
        lastError = SqlError(message, code);
    }

    void clearError() {
        lastError.clear();
    }
};

//----------------------------------------------------------------------
// Constructors and Destructors
//----------------------------------------------------------------------

SqlTable::SqlTable()
    : SqlObject("", SqlObjectType::Table) {
}

SqlTable::SqlTable(std::string_view name)
    : SqlObject(name, SqlObjectType::Table) {
}

SqlTable::SqlTable(std::string_view name, std::shared_ptr<SqlDatabase> db)
    : SqlObject(name, SqlObjectType::Table) {
    if (db) {
        if (!d_ptr) {
            d_ptr = std::make_shared<SqlTablePrivate>();
        }
        d_ptr->setDatabase(db);
    }
}

SqlTable::SqlTable(std::string_view name, std::string_view schema, std::string_view alias) noexcept
    : SqlObject(name, SqlObjectType::Table) {
    // Initialize metadata with schema information
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlTableMetadata>();
    }
    m_metadata->schema = schema;
    // Note: alias handling might need to be added to metadata structure
}

SqlTable::~SqlTable() = default;

// Copy constructor
SqlTable::SqlTable(const SqlTable& other)
    : SqlObject(other), m_metadata(other.m_metadata), d_ptr(other.d_ptr) {
}

// Copy assignment operator
SqlTable& SqlTable::operator=(const SqlTable& other) {
    if (this != &other) {
        SqlObject::operator=(other);
        m_metadata = other.m_metadata;
        d_ptr = other.d_ptr;
    }
    return *this;
}

// Move constructor
SqlTable::SqlTable(SqlTable&& other) noexcept
    : SqlObject(std::move(other)), m_metadata(std::move(other.m_metadata)), d_ptr(std::move(other.d_ptr)) {
}

// Move assignment operator
SqlTable& SqlTable::operator=(SqlTable&& other) noexcept {
    if (this != &other) {
        SqlObject::operator=(std::move(other));
        m_metadata = std::move(other.m_metadata);
        d_ptr = std::move(other.d_ptr);
    }
    return *this;
}

//----------------------------------------------------------------------
// Static Factory Methods
//----------------------------------------------------------------------

SqlTable SqlTable::fromDatabase(std::string_view name, std::shared_ptr<SqlDatabase> db) {
    SqlTable table(name, db);

    if (db) {
        // Load metadata from database
        table.loadMetadata();
    }

    return table;
}

std::vector<SqlTable> SqlTable::allTables(std::shared_ptr<SqlDatabase> db,
                                          std::string_view schema,
                                          SqlObjectType type) {
    std::vector<SqlTable> tables;

    if (!db || !db->isOpen()) {
        return tables;
    }

    try {
        SqlQuery query(*db);

        // Build query to get table information
        std::string sql = "SELECT TABLE_NAME, TABLE_SCHEMA FROM INFORMATION_SCHEMA.TABLES WHERE 1=1";

        std::vector<Data> parameters;

        if (!schema.empty()) {
            sql += " AND TABLE_SCHEMA = ?";
            parameters.push_back(std::string(schema));
        }

        if (type == SqlObjectType::Table) {
            sql += " AND TABLE_TYPE = 'BASE TABLE'";
        } else if (type == SqlObjectType::View) {
            sql += " AND TABLE_TYPE = 'VIEW'";
        }

        query.setQuery(sql).prepare();

        // Bind parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(i + 1), parameters[i]);
        }

        if (!query.execute()) {
            return tables;
        }

        while (query.next()) {
            std::string tableName = query.value("TABLE_NAME").to<std::string>();
            std::string tableSchema = query.value("TABLE_SCHEMA").to<std::string>();

            SqlTable table = SqlTable::fromDatabase(tableName, db);
            table.setSchema(tableSchema);
            tables.push_back(std::move(table));
        }

    } catch (const std::exception&) {
        // Return empty vector on error
    }

    return tables;
}

//----------------------------------------------------------------------
// Table Metadata
//----------------------------------------------------------------------

SqlTableMetadata* SqlTable::metadata() const noexcept {
    // Lazy initialization of metadata
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlTableMetadata>();
    }
    return m_metadata.get();
}

SqlTable& SqlTable::setMetadata(const SqlTableMetadata& metadata) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlTableMetadata>();
    }
    *m_metadata = metadata;
    return *this;
}

bool SqlTable::hasMetadata() const noexcept {
    return m_metadata != nullptr;
}

bool SqlTable::loadMetadata() {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query database for table metadata
        std::string sql = "SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?";

        query.setQuery(sql)
             .prepare()
             .bind(1, std::string(name()))
             .bind(2, metadata()->schema.empty() ? "public" : metadata()->schema);

        if (!query.execute()) {
            d_ptr->setError("Failed to load table metadata: " + query.lastError().message());
            return false;
        }

        if (query.next()) {
            // Initialize metadata if not exists
            if (!m_metadata) {
                m_metadata = std::make_shared<SqlTableMetadata>();
            }

            // Extract metadata from query result
            m_metadata->schema = query.value("TABLE_SCHEMA").to<std::string>();
            m_metadata->engine = query.value("ENGINE").to<std::string>();
            m_metadata->comment = query.value("TABLE_COMMENT").to<std::string>();

            // Additional fields as available
            if (!query.value("TABLE_ROWS").isNull()) {
                m_metadata->rowCount = query.value("TABLE_ROWS").to<size_t>();
            }

            d_ptr->clearError();
            return true;
        }

        d_ptr->setError("Table not found in database");
        return false;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception loading metadata: " + std::string(e.what()));
        return false;
    }
}

//----------------------------------------------------------------------
// Metadata Accessor Methods
//----------------------------------------------------------------------

std::string SqlTable::schema() const {
    return metadata()->schema;
}

SqlTable& SqlTable::setSchema(std::string_view schema) {
    metadata()->schema = schema;
    return *this;
}

std::string SqlTable::engine() const noexcept {
    return metadata()->engine;
}

SqlTable& SqlTable::setEngine(std::string_view engine) {
    metadata()->engine = engine;
    return *this;
}

std::string SqlTable::catalog() const noexcept {
    return metadata()->catalog;
}

SqlTable& SqlTable::setCatalog(std::string_view catalog) {
    metadata()->catalog = catalog;
    return *this;
}

std::string SqlTable::comment() const noexcept {
    return metadata()->comment;
}

SqlTable& SqlTable::setComment(std::string_view comment) {
    metadata()->comment = comment;
    return *this;
}

std::string SqlTable::charset() const noexcept {
    return metadata()->charset;
}

SqlTable& SqlTable::setCharset(std::string_view charset) {
    metadata()->charset = charset;
    return *this;
}

std::string SqlTable::collation() const noexcept {
    return metadata()->collation;
}

SqlTable& SqlTable::setCollation(std::string_view collation) {
    metadata()->collation = collation;
    return *this;
}

bool SqlTable::isTemporary() const noexcept {
    return metadata()->isTemporary;
}

SqlTable& SqlTable::setTemporary(bool temporary) {
    metadata()->isTemporary = temporary;
    return *this;
}

//----------------------------------------------------------------------
// Column Management
//----------------------------------------------------------------------

std::vector<SqlColumn> SqlTable::columns() const noexcept {
    return metadata()->columns;
}

SqlColumn SqlTable::column(std::string_view name) {
    // First check if column exists in metadata
    auto& columnMap = metadata()->m_columnMap;
    auto it = columnMap.find(std::string(name));

    if (it != columnMap.end()) {
        return metadata()->columns[it->second];
    }

    // If not found and we have a database connection, try to load from database
    if (d_ptr && d_ptr->isValid()) {
        SqlColumn col = SqlColumn::fromDatabase(name, d_ptr->database.get());
        col.setTable(*this);

        // Add to metadata
        addColumn(col);
        return col;
    }

    // Return a new column with this table association
    SqlColumn col(name, *this);
    return col;
}

SqlColumn SqlTable::column(std::string_view name) const {
    // Check if column exists in metadata
    auto& columnMap = metadata()->m_columnMap;
    auto it = columnMap.find(std::string(name));

    if (it != columnMap.end()) {
        return metadata()->columns[it->second];
    }

    // Return a new column with this table association
    SqlColumn col(name, *this);
    return col;
}

SqlTable& SqlTable::addColumn(const SqlColumn& column) {
    auto& columns = metadata()->columns;
    auto& columnMap = metadata()->m_columnMap;

    std::string columnName = std::string(column.name());

    // Check if column already exists
    auto it = columnMap.find(columnName);
    if (it != columnMap.end()) {
        // Update existing column
        columns[it->second] = column;
    } else {
        // Add new column
        columnMap[columnName] = columns.size();
        columns.push_back(column);
    }

    return *this;
}

SqlTable& SqlTable::dropColumn(std::string_view name) {
    auto& columns = metadata()->columns;
    auto& columnMap = metadata()->m_columnMap;

    std::string columnName = std::string(name);
    auto it = columnMap.find(columnName);

    if (it != columnMap.end()) {
        size_t index = it->second;

        // Remove from vector
        columns.erase(columns.begin() + index);

        // Remove from map
        columnMap.erase(it);

        // Update indices in map for columns after the removal point
        for (size_t i = index; i < columns.size(); ++i) {
            columnMap[std::string(columns[i].name())] = i;
        }

        // If we have a database connection, drop the column from the database
        if (d_ptr && d_ptr->isValid()) {
            try {
                SqlQuery query(*d_ptr->database);
                std::string sql = "ALTER TABLE " + qualifiedName() + " DROP COLUMN " + columnName;
                query.setQuery(sql);
                query.execute(); // Ignore errors for now
            } catch (...) {
                // Ignore database errors for metadata operations
            }
        }
    }

    return *this;
}

SqlTable& SqlTable::modifyColumn(const SqlColumn& column) {
    // Update in metadata
    addColumn(column);

    // If we have a database connection, modify the column in the database
    if (d_ptr && d_ptr->isValid()) {
        try {
            SqlQuery query(*d_ptr->database);
            std::string sql = "ALTER TABLE " + qualifiedName() + " MODIFY COLUMN " + column.definitionSql();
            query.setQuery(sql);
            query.execute(); // Ignore errors for now
        } catch (...) {
            // Ignore database errors for metadata operations
        }
    }

    return *this;
}

bool SqlTable::hasColumn(std::string_view name) const noexcept {
    auto& columnMap = metadata()->m_columnMap;
    return columnMap.find(std::string(name)) != columnMap.end();
}

size_t SqlTable::columnCount() const noexcept {
    return metadata()->columns.size();
}

//----------------------------------------------------------------------
// Index Management
//----------------------------------------------------------------------

std::vector<SqlIndex> SqlTable::indexes() const {
    return metadata()->indexes;
}

SqlIndex SqlTable::index(std::string_view indexName) const {
    // Search for index in metadata
    auto& indexes = metadata()->indexes;
    auto it = std::find_if(indexes.begin(), indexes.end(),
                          [indexName](const SqlIndex& idx) {
                              return idx.name() == indexName;
                          });

    if (it != indexes.end()) {
        return *it;
    }

    // Return empty index if not found
    return SqlIndex{};
}

bool SqlTable::hasIndex(std::string_view indexName) const {
    auto& indexes = metadata()->indexes;
    return std::any_of(indexes.begin(), indexes.end(),
                      [indexName](const SqlIndex& idx) {
                          return idx.name() == indexName;
                      });
}

bool SqlTable::addIndex(const SqlIndex& index) {
    // Add to metadata
    auto& indexes = metadata()->indexes;

    // Check if index already exists
    auto it = std::find_if(indexes.begin(), indexes.end(),
                          [&index](const SqlIndex& idx) {
                              return idx.name() == index.name();
                          });

    if (it != indexes.end()) {
        // Update existing index
        *it = index;
    } else {
        // Add new index
        indexes.push_back(index);
    }

    // If we have a database connection, create the index in the database
    if (d_ptr && d_ptr->isValid()) {
        try {
            SqlQuery query(*d_ptr->database);
            std::string sql = index.createSql();
            query.setQuery(sql);
            return query.execute();
        } catch (...) {
            return false;
        }
    }

    return true;
}

bool SqlTable::addIndex(std::string_view indexName, const std::vector<std::string>& columnNames, bool unique) {
    // Create SqlIndex object
    SqlIndex index(indexName);
    index.setTableName(std::string(name()));
    index.setColumns(columnNames);
    index.setIndexType(unique ? SqlIndexType::Unique : SqlIndexType::Normal);

    return addIndex(index);
}

bool SqlTable::dropIndex(std::string_view indexName) {
    // Remove from metadata
    auto& indexes = metadata()->indexes;
    auto it = std::find_if(indexes.begin(), indexes.end(),
                          [indexName](const SqlIndex& idx) {
                              return idx.name() == indexName;
                          });

    if (it != indexes.end()) {
        indexes.erase(it);
    }

    // If we have a database connection, drop the index from the database
    if (d_ptr && d_ptr->isValid()) {
        try {
            SqlQuery query(*d_ptr->database);
            std::string sql = "DROP INDEX " + std::string(indexName) + " ON " + qualifiedName();
            query.setQuery(sql);
            return query.execute();
        } catch (...) {
            return false;
        }
    }

    return true;
}

//----------------------------------------------------------------------
// Row Management
//----------------------------------------------------------------------

size_t SqlTable::rowCount() const {
    if (!d_ptr || !d_ptr->isValid()) {
        // Return cached value if available
        if (metadata()->rowCount.has_value()) {
            return metadata()->rowCount.value();
        }
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);
        std::string sql = "SELECT COUNT(*) FROM " + qualifiedName();
        query.setQuery(sql);

        if (!query.execute() || !query.next()) {
            return 0;
        }

        return query.value(0).to<size_t>();

    } catch (const std::exception&) {
        return 0;
    }
}

std::vector<SqlRow> SqlTable::selectAll() const {
    std::vector<SqlRow> rows;

    if (!d_ptr || !d_ptr->isValid()) {
        return rows;
    }

    try {
        SqlQuery query(*d_ptr->database);
        std::string sql = "SELECT * FROM " + qualifiedName();
        query.setQuery(sql);

        if (!query.execute()) {
            return rows;
        }

        while (query.next()) {
            SqlRow row = query.record();
            rows.push_back(std::move(row));
        }

    } catch (const std::exception&) {
        // Return empty vector on error
    }

    return rows;
}

std::vector<SqlRow> SqlTable::select(const std::string& whereClause, const std::vector<Data>& parameters) const {
    std::vector<SqlRow> rows;

    if (!d_ptr || !d_ptr->isValid()) {
        return rows;
    }

    try {
        SqlQuery query(*d_ptr->database);
        std::string sql = "SELECT * FROM " + qualifiedName();

        if (!whereClause.empty()) {
            sql += " WHERE " + whereClause;
        }

        query.setQuery(sql).prepare();

        // Bind parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(i + 1), parameters[i]);
        }

        if (!query.execute()) {
            return rows;
        }

        while (query.next()) {
            SqlRow row = query.record();
            rows.push_back(std::move(row));
        }

    } catch (const std::exception&) {
        // Return empty vector on error
    }

    return rows;
}

bool SqlTable::insert(const SqlRow& row) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build INSERT statement
        auto fieldNames = row.fieldNames();
        if (fieldNames.empty()) {
            d_ptr->setError("Row has no fields");
            return false;
        }

        std::string sql = "INSERT INTO " + qualifiedName() + " (";
        for (size_t i = 0; i < fieldNames.size(); ++i) {
            if (i > 0) sql += ", ";
            sql += fieldNames[i];
        }
        sql += ") VALUES (";
        for (size_t i = 0; i < fieldNames.size(); ++i) {
            if (i > 0) sql += ", ";
            sql += "?";
        }
        sql += ")";

        query.setQuery(sql).prepare();

        // Bind values
        for (size_t i = 0; i < fieldNames.size(); ++i) {
            query.bind(static_cast<int>(i + 1), row.value(fieldNames[i]));
        }

        if (!query.execute()) {
            d_ptr->setError("Failed to insert row: " + query.lastError().message());
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception inserting row: " + std::string(e.what()));
        return false;
    }
}

size_t SqlTable::insert(const std::vector<SqlRow>& rows) {
    if (rows.empty()) {
        return 0;
    }

    size_t insertedCount = 0;

    // For now, insert rows one by one
    // TODO: Implement batch insert for better performance
    for (const auto& row : rows) {
        if (insert(row)) {
            ++insertedCount;
        }
    }

    return insertedCount;
}

size_t SqlTable::update(const SqlRow& row, const std::string& whereClause, const std::vector<Data>& parameters) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build UPDATE statement
        auto fieldNames = row.fieldNames();
        if (fieldNames.empty()) {
            d_ptr->setError("Row has no fields");
            return 0;
        }

        std::string sql = "UPDATE " + qualifiedName() + " SET ";
        for (size_t i = 0; i < fieldNames.size(); ++i) {
            if (i > 0) sql += ", ";
            sql += fieldNames[i] + " = ?";
        }

        if (!whereClause.empty()) {
            sql += " WHERE " + whereClause;
        }

        query.setQuery(sql).prepare();

        // Bind field values
        for (size_t i = 0; i < fieldNames.size(); ++i) {
            query.bind(static_cast<int>(i + 1), row.value(fieldNames[i]));
        }

        // Bind WHERE clause parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(fieldNames.size() + i + 1), parameters[i]);
        }

        if (!query.execute()) {
            d_ptr->setError("Failed to update rows: " + query.lastError().message());
            return 0;
        }

        d_ptr->clearError();
        return static_cast<size_t>(query.numRowsAffected());

    } catch (const std::exception& e) {
        d_ptr->setError("Exception updating rows: " + std::string(e.what()));
        return 0;
    }
}

size_t SqlTable::deleteRows(const std::string& whereClause, const std::vector<Data>& parameters) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build DELETE statement
        std::string sql = "DELETE FROM " + qualifiedName();

        if (!whereClause.empty()) {
            sql += " WHERE " + whereClause;
        }

        query.setQuery(sql).prepare();

        // Bind parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(i + 1), parameters[i]);
        }

        if (!query.execute()) {
            d_ptr->setError("Failed to delete rows: " + query.lastError().message());
            return 0;
        }

        d_ptr->clearError();
        return static_cast<size_t>(query.numRowsAffected());

    } catch (const std::exception& e) {
        d_ptr->setError("Exception deleting rows: " + std::string(e.what()));
        return 0;
    }
}

//----------------------------------------------------------------------
// Database Interaction Operations
//----------------------------------------------------------------------

std::shared_ptr<SqlDatabase> SqlTable::database() const {
    if (d_ptr) {
        return d_ptr->database;
    }
    return nullptr;
}

void SqlTable::setDatabase(std::shared_ptr<SqlDatabase> database) {
    if (!d_ptr) {
        d_ptr = std::make_shared<SqlTablePrivate>();
    }
    d_ptr->setDatabase(database);
}

bool SqlTable::exists() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query to check if table exists
        std::string sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?";

        query.setQuery(sql)
             .prepare()
             .bind(1, std::string(name()))
             .bind(2, metadata()->schema.empty() ? "public" : metadata()->schema);

        if (!query.execute() || !query.next()) {
            return false;
        }

        return query.value(0).to<int>() > 0;

    } catch (const std::exception&) {
        return false;
    }
}

bool SqlTable::create(const std::vector<SqlColumn>& columns, bool ifNotExists) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build CREATE TABLE statement
        std::string sql = createSql(ifNotExists);

        // If columns are provided, use them; otherwise use metadata columns
        if (!columns.empty()) {
            // Update metadata with provided columns
            metadata()->columns = columns;
            metadata()->m_columnMap.clear();
            for (size_t i = 0; i < columns.size(); ++i) {
                metadata()->m_columnMap[std::string(columns[i].name())] = i;
            }
        }

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError("Failed to create table: " + query.lastError().message());
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception creating table: " + std::string(e.what()));
        return false;
    }
}

bool SqlTable::drop(bool ifExists) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build DROP TABLE statement
        std::string sql = dropSql(ifExists);

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError("Failed to drop table: " + query.lastError().message());
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception dropping table: " + std::string(e.what()));
        return false;
    }
}

bool SqlTable::truncate() {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build TRUNCATE TABLE statement
        std::string sql = "TRUNCATE TABLE " + qualifiedName();

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError("Failed to truncate table: " + query.lastError().message());
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception truncating table: " + std::string(e.what()));
        return false;
    }
}

bool SqlTable::execute(const std::string& sql, const std::vector<Data>& parameters) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        query.setQuery(sql).prepare();

        // Bind parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(i + 1), parameters[i]);
        }

        if (!query.execute()) {
            d_ptr->setError("Failed to execute SQL: " + query.lastError().message());
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception executing SQL: " + std::string(e.what()));
        return false;
    }
}

bool SqlTable::refreshMetadata() {
    // Clear existing metadata and reload
    if (m_metadata) {
        m_metadata.reset();
    }
    return loadMetadata();
}

bool SqlTable::isEmpty() const {
    return rowCount() == 0;
}

//----------------------------------------------------------------------
// SqlObject Implementation
//----------------------------------------------------------------------

std::string SqlTable::qualifiedName() const {
    if (metadata()->schema.empty()) {
        return std::string(name());
    }
    return metadata()->schema + "." + std::string(name());
}

std::string SqlTable::toSql() const {
    return qualifiedName();
}

std::string SqlTable::createSql(bool ifNotExists) const {
    std::ostringstream sql;

    sql << "CREATE TABLE ";
    if (ifNotExists) {
        sql << "IF NOT EXISTS ";
    }
    sql << qualifiedName() << " (";

    // Add columns
    auto& columns = metadata()->columns;
    if (columns.empty()) {
        // If no columns in metadata, create a minimal table
        sql << "id INTEGER PRIMARY KEY";
    } else {
        for (size_t i = 0; i < columns.size(); ++i) {
            if (i > 0) sql << ", ";
            sql << columns[i].definitionSql();
        }
    }

    sql << ")";

    // Add table options
    if (!metadata()->engine.empty()) {
        sql << " ENGINE=" << metadata()->engine;
    }

    if (!metadata()->charset.empty()) {
        sql << " DEFAULT CHARSET=" << metadata()->charset;
    }

    if (!metadata()->collation.empty()) {
        sql << " COLLATE=" << metadata()->collation;
    }

    if (!metadata()->comment.empty()) {
        sql << " COMMENT='" << metadata()->comment << "'";
    }

    return sql.str();
}

std::string SqlTable::alterSql() const {
    // TODO: Implement ALTER TABLE statement generation
    // This would require tracking changes to the table structure
    return "ALTER TABLE " + qualifiedName() + " /* TODO: implement alter logic */";
}

std::string SqlTable::dropSql(bool ifExists) const {
    std::string sql = "DROP TABLE ";
    if (ifExists) {
        sql += "IF EXISTS ";
    }
    sql += qualifiedName();
    return sql;
}

//----------------------------------------------------------------------
// Helper Methods
//----------------------------------------------------------------------

void SqlTable::ensureMetadata() const {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlTableMetadata>();
    }
}

void SqlTable::ensureDatabase() const {
    if (!d_ptr) {
        d_ptr = std::make_shared<SqlTablePrivate>();
    }
}

} // namespace database
