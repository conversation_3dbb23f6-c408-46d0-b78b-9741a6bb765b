﻿#ifndef DATABASE_SQL_TABLE_H
#define DATABASE_SQL_TABLE_H

#include <string>
#include <string_view>
#include <vector>
#include <memory>
#include <optional>
#include <chrono>

#include "sql_database.h"
#include "sql_enums.h"
#include "sql_index.h"
#include "sql_object.h"

namespace database {

// Forward declarations
class SqlTablePrivate;
class SqlColumn;
class SqlIndex;
class SqlRow;

/**
 * @brief Class representing a database table
 *
 * This class operates in two distinct modes:
 * 1. Builder Mode: Lightweight SQL statement construction without database access
 * 2. Metadata Mode: Full database metadata access via driver delegation
 *
 */
class SqlTable final : public SqlObject {
public:
    /**
     * @brief Table metadata structure
     */
    struct SqlTableMetadata {
        std::string schema;                                 ///< Schema name
        std::string engine;    ///< Storage engine (e.g., InnoDB, MyISAM)
        std::string catalog;   ///< Catalog name
        std::string comment;   ///< Table comment/description
        std::string charset;   ///< Character set
        std::string collation; ///< Collation

        std::optional<size_t> rowCount;                                    ///< Estimated row count
        std::optional<size_t> dataLength;                                  ///< Data length in bytes
        std::optional<size_t> indexLength;                                 ///< Index length in bytes
        std::optional<size_t> autoIncrementValue;                          ///< Next auto increment value
        std::vector<std::string> partitionKeys;                            ///< Partition keys
        bool isTemporary = false;                                          ///< Is temporary table
        bool isView = false;                                               ///< Is view (materialized or regular)

        std::optional<std::chrono::system_clock::time_point> createdTime;  ///< Creation time
        std::optional<std::chrono::system_clock::time_point> modifiedTime; ///< Last modification time

        std::unordered_map<std::string, std::string> properties;           ///< Additional properties

        std::vector<SqlColumn> columns;
        std::unordered_map<std::string, size_t> m_columnMap; // Maps column names to indices
        std::vector<SqlIndex> indexes;

        // SqlTableMetadata() = default;
        // SqlTableMetadata(std::string schema, std::string comment = {})
        //     : schema(std::move(schema)), comment(std::move(comment)) {}
    };

    // Constructors
    SqlTable();
    explicit SqlTable(std::string_view name);
    SqlTable(std::string_view name, std::shared_ptr<SqlDatabase> db);
    SqlTable(std::string_view name, std::string_view schema, std::string_view alias = {}) noexcept;

    ~SqlTable();

    // Default copy/move operations
    SqlTable(const SqlTable& other);
    SqlTable& operator=(const SqlTable& other);
    SqlTable(SqlTable&& other) noexcept;
    SqlTable& operator=(SqlTable&& other) noexcept;

    [[nodiscard]] static SqlTable fromDatabase(std::string_view name, std::shared_ptr<SqlDatabase> db);

    [[nodiscard]] static std::vector<SqlTable> allTables(std::shared_ptr<SqlDatabase> db,
                                                         std::string_view schema = "",
                                                         SqlObjectType type = SqlObjectType::Table);

    //----------------------------------------------------------------------
    // Table Metadata
    //----------------------------------------------------------------------
    [[nodiscard]] SqlTableMetadata* metadata() const noexcept;
    SqlTable& setMetadata(const SqlTableMetadata& metadata);
    bool hasMetadata() const noexcept;
    bool loadMetadata();

    [[nodiscard]] std::string schema() const;
    SqlTable& setSchema(std::string_view schema);
    [[nodiscard]] std::string engine() const noexcept;
    SqlTable& setEngine(std::string_view engine);
    [[nodiscard]] std::string catalog() const noexcept;
    SqlTable& setCatalog(std::string_view catalog);
    [[nodiscard]] std::string comment() const noexcept;
    SqlTable& setComment(std::string_view comment);
    [[nodiscard]] std::string charset() const noexcept;
    SqlTable& setCharset(std::string_view charset);
    [[nodiscard]] std::string collation() const noexcept;
    SqlTable& setCollation(std::string_view collation);

    [[nodiscard]] bool isTemporary() const noexcept;
    SqlTable& setTemporary(bool temporary);

    //----------------------------------------------------------------------
    // Column Management
    //----------------------------------------------------------------------
    [[nodiscard]] std::vector<SqlColumn> columns() const noexcept;

    [[nodiscard]] SqlColumn column(std::string_view name);
    [[nodiscard]] SqlColumn column(std::string_view name) const;

    SqlTable& addColumn(const SqlColumn& column);
    SqlTable& dropColumn(std::string_view name);
    SqlTable& modifyColumn(const SqlColumn& column);

    [[nodiscard]] bool hasColumn(std::string_view name) const noexcept;
    [[nodiscard]] size_t columnCount() const noexcept;

    // [[nodiscard]] SqlColumn all() const;

    //----------------------------------------------------------------------
    // Index Management
    //----------------------------------------------------------------------
    [[nodiscard]] std::vector<SqlIndex> indexes() const;
    [[nodiscard]] SqlIndex index(std::string_view indexName) const;
    [[nodiscard]] bool hasIndex(std::string_view indexName) const;

    bool addIndex(const SqlIndex& index);
    bool addIndex(std::string_view indexName, const std::vector<std::string>& columnNames, bool unique = false);
    bool dropIndex(std::string_view indexName);

    //----------------------------------------------------------------------
    // Row Management
    //----------------------------------------------------------------------
    [[nodiscard]] size_t rowCount() const;
    [[nodiscard]] std::vector<SqlRow> selectAll() const;
    [[nodiscard]] std::vector<SqlRow> select(const std::string& whereClause,
                                             const std::vector<Data>& parameters = {}) const;
    bool insert(const SqlRow& row);
    size_t insert(const std::vector<SqlRow>& rows);
    size_t update(const SqlRow& row, const std::string& whereClause, const std::vector<Data>& parameters = {});
    size_t deleteRows(const std::string& whereClause, const std::vector<Data>& parameters = {});

    //----------------------------------------------------------------------
    // Database Interaction Operations
    //----------------------------------------------------------------------
    [[nodiscard]] std::shared_ptr<SqlDatabase> database() const;
    void setDatabase(std::shared_ptr<SqlDatabase> database);

    [[nodiscard]] bool exists() const;
    bool create(const std::vector<SqlColumn>& columns, bool ifNotExists = true);
    bool drop(bool ifExists = true);
    bool truncate();

    bool execute(const std::string& sql, const std::vector<Data>& parameters = {});
    bool refreshMetadata();
    [[nodiscard]] bool isEmpty() const;

    //----------------------------------------------------------------------
    // SqlObject Implementation
    //----------------------------------------------------------------------
    [[nodiscard]] std::string qualifiedName() const;
    [[nodiscard]] std::string toSql() const override;

protected:
    // bool loadFromDatabase() override;
    // [[nodiscard]] bool existsInDatabase() const override;

    [[nodiscard]] std::string createSql(bool ifNotExists = false) const;
    [[nodiscard]] std::string alterSql() const;
    [[nodiscard]] std::string dropSql(bool ifExists = false) const;

private:
    mutable std::shared_ptr<SqlTableMetadata> m_metadata;
    mutable std::shared_ptr<SqlTablePrivate> d_ptr;

    // Helper methods
    void ensureMetadata() const;
    void ensureDatabase() const;
};

} // namespace database


#endif // DATABASE_SQL_TABLE_H
