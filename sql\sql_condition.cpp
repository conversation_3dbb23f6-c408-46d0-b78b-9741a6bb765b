#include "sql_condition.h"
#include <sstream>
#include <algorithm>

namespace database {

//----------------------------------------------------------------------
// Constructors
//----------------------------------------------------------------------

SqlCondition::SqlCondition(std::string_view columnName, SqlConditionOperator op, const Variant& value)
    : m_columnName(columnName), m_operator(op), m_value(value), m_isCompound(false) {
}

SqlCondition::SqlCondition(std::string_view columnName, SqlConditionOperator op, const std::vector<Variant>& values)
    : m_columnName(columnName), m_operator(op), m_values(values), m_isCompound(false) {
}

SqlCondition::SqlCondition(std::string_view columnName, const Variant& minValue, const Variant& maxValue)
    : m_columnName(columnName), m_operator(SqlConditionOperator::Between), m_isCompound(false) {
    m_values = {minValue, maxValue};
}

SqlCondition::SqlCondition(std::string_view columnName, SqlConditionOperator op)
    : m_columnName(columnName), m_operator(op), m_isCompound(false) {
    // For IS NULL and IS NOT NULL operators
}

//----------------------------------------------------------------------
// Property Accessors
//----------------------------------------------------------------------

bool SqlCondition::isValid() const noexcept {
    return !m_columnName.empty() || m_isCompound;
}

const std::string& SqlCondition::columnName() const noexcept {
    return m_columnName;
}

SqlConditionOperator SqlCondition::operatorType() const noexcept {
    return m_operator;
}

const Variant& SqlCondition::value() const noexcept {
    return m_value;
}

const std::vector<Variant>& SqlCondition::values() const noexcept {
    return m_values;
}

//----------------------------------------------------------------------
// SQL Generation
//----------------------------------------------------------------------

std::string SqlCondition::toSql() const {
    if (!isValid()) {
        return "";
    }
    
    if (m_isCompound) {
        std::string result;
        
        if (m_isNegated) {
            result += "NOT (";
        }
        
        if (m_leftCondition) {
            result += m_leftCondition->toSql();
        }
        
        if (m_leftCondition && m_rightCondition) {
            switch (m_logicalOperator) {
                case SqlLogicalOperator::And:
                    result += " AND ";
                    break;
                case SqlLogicalOperator::Or:
                    result += " OR ";
                    break;
                case SqlLogicalOperator::Not:
                    result += " NOT ";
                    break;
            }
        }
        
        if (m_rightCondition) {
            result += m_rightCondition->toSql();
        }
        
        if (m_isNegated) {
            result += ")";
        }
        
        return result;
    }
    
    std::ostringstream sql;
    sql << m_columnName << " " << sqlOperatorToString(m_operator);
    
    switch (m_operator) {
        case SqlConditionOperator::Equal:
        case SqlConditionOperator::NotEqual:
        case SqlConditionOperator::LessThan:
        case SqlConditionOperator::LessEqual:
        case SqlConditionOperator::GreaterThan:
        case SqlConditionOperator::GreaterEqual:
        case SqlConditionOperator::Like:
        case SqlConditionOperator::NotLike:
            sql << " " << formatValue(m_value);
            break;
            
        case SqlConditionOperator::In:
        case SqlConditionOperator::NotIn:
            sql << " (" << formatValues(m_values) << ")";
            break;
            
        case SqlConditionOperator::Between:
            if (m_values.size() >= 2) {
                sql << " " << formatValue(m_values[0]) << " AND " << formatValue(m_values[1]);
            }
            break;
            
        case SqlConditionOperator::IsNull:
        case SqlConditionOperator::IsNotNull:
            // No additional value needed
            break;
    }
    
    return sql.str();
}

std::string SqlCondition::toSql(std::vector<Variant>& parameters) const {
    if (!isValid()) {
        return "";
    }
    
    if (m_isCompound) {
        std::string result;
        
        if (m_isNegated) {
            result += "NOT (";
        }
        
        if (m_leftCondition) {
            result += m_leftCondition->toSql(parameters);
        }
        
        if (m_leftCondition && m_rightCondition) {
            switch (m_logicalOperator) {
                case SqlLogicalOperator::And:
                    result += " AND ";
                    break;
                case SqlLogicalOperator::Or:
                    result += " OR ";
                    break;
                case SqlLogicalOperator::Not:
                    result += " NOT ";
                    break;
            }
        }
        
        if (m_rightCondition) {
            result += m_rightCondition->toSql(parameters);
        }
        
        if (m_isNegated) {
            result += ")";
        }
        
        return result;
    }
    
    std::ostringstream sql;
    sql << m_columnName << " " << sqlOperatorToString(m_operator);
    
    switch (m_operator) {
        case SqlConditionOperator::Equal:
        case SqlConditionOperator::NotEqual:
        case SqlConditionOperator::LessThan:
        case SqlConditionOperator::LessEqual:
        case SqlConditionOperator::GreaterThan:
        case SqlConditionOperator::GreaterEqual:
        case SqlConditionOperator::Like:
        case SqlConditionOperator::NotLike:
            sql << " ?";
            parameters.push_back(m_value);
            break;
            
        case SqlConditionOperator::In:
        case SqlConditionOperator::NotIn:
            sql << " (";
            for (size_t i = 0; i < m_values.size(); ++i) {
                if (i > 0) sql << ", ";
                sql << "?";
                parameters.push_back(m_values[i]);
            }
            sql << ")";
            break;
            
        case SqlConditionOperator::Between:
            if (m_values.size() >= 2) {
                sql << " ? AND ?";
                parameters.push_back(m_values[0]);
                parameters.push_back(m_values[1]);
            }
            break;
            
        case SqlConditionOperator::IsNull:
        case SqlConditionOperator::IsNotNull:
            // No additional parameters needed
            break;
    }
    
    return sql.str();
}

//----------------------------------------------------------------------
// Logical Operators
//----------------------------------------------------------------------

SqlCondition SqlCondition::operator&&(const SqlCondition& other) const {
    return And(*this, other);
}

SqlCondition SqlCondition::operator||(const SqlCondition& other) const {
    return Or(*this, other);
}

SqlCondition SqlCondition::operator!() const {
    return Not(*this);
}

SqlCondition SqlCondition::And(const SqlCondition& left, const SqlCondition& right) {
    SqlCondition result;
    result.m_isCompound = true;
    result.m_logicalOperator = SqlLogicalOperator::And;
    result.m_leftCondition = std::make_shared<SqlCondition>(left);
    result.m_rightCondition = std::make_shared<SqlCondition>(right);
    return result;
}

SqlCondition SqlCondition::Or(const SqlCondition& left, const SqlCondition& right) {
    SqlCondition result;
    result.m_isCompound = true;
    result.m_logicalOperator = SqlLogicalOperator::Or;
    result.m_leftCondition = std::make_shared<SqlCondition>(left);
    result.m_rightCondition = std::make_shared<SqlCondition>(right);
    return result;
}

SqlCondition SqlCondition::Not(const SqlCondition& condition) {
    SqlCondition result = condition;
    result.m_isNegated = !result.m_isNegated;
    return result;
}

//----------------------------------------------------------------------
// Helper Methods
//----------------------------------------------------------------------

std::string SqlCondition::formatValue(const Variant& value) const {
    if (value.isString()) {
        return "'" + value.to<std::string>() + "'";
    } else if (value.isNull()) {
        return "NULL";
    } else {
        return value.to<std::string>();
    }
}

std::string SqlCondition::formatValues(const std::vector<Variant>& values) const {
    std::ostringstream result;
    for (size_t i = 0; i < values.size(); ++i) {
        if (i > 0) result << ", ";
        result << formatValue(values[i]);
    }
    return result.str();
}

} // namespace database
