#include "sql/sql_view.h"
#include "sql/sql_database.h"
#include "sql/sql_row.h"
#include <iostream>

using namespace database;

int main() {
    try {
        // Test basic view creation
        SqlView view1("user_summary");
        std::cout << "Created view: " << view1.name() << std::endl;
        
        // Test with definition
        std::string definition = "SELECT id, name, email FROM users WHERE active = 1";
        SqlView view2("active_users", definition);
        std::cout << "View definition: " << view2.definition() << std::endl;
        
        // Test metadata
        view2.setSchema("public");
        view2.setComment("View showing only active users");
        view2.setUpdatable(true);
        view2.setDefiner("admin@localhost");
        view2.setSecurityType("DEFINER");
        
        std::cout << "Schema: " << view2.schema() << std::endl;
        std::cout << "Comment: " << view2.comment() << std::endl;
        std::cout << "Is updatable: " << view2.isUpdatable() << std::endl;
        std::cout << "Is materialized: " << view2.isMaterialized() << std::endl;
        std::cout << "Definer: " << view2.definer() << std::endl;
        std::cout << "Security type: " << view2.securityType() << std::endl;
        
        // Test column names
        std::vector<std::string> columns = {"id", "name", "email", "status"};
        view2.setColumnNames(columns);
        
        auto viewColumns = view2.columnNames();
        std::cout << "Column count: " << viewColumns.size() << std::endl;
        std::cout << "Columns: ";
        for (size_t i = 0; i < viewColumns.size(); ++i) {
            if (i > 0) std::cout << ", ";
            std::cout << viewColumns[i];
        }
        std::cout << std::endl;
        
        // Test referenced tables
        std::vector<std::string> tables = {"users", "user_profiles"};
        view2.setReferencedTables(tables);
        
        auto refTables = view2.referencedTables();
        std::cout << "Referenced tables: ";
        for (size_t i = 0; i < refTables.size(); ++i) {
            if (i > 0) std::cout << ", ";
            std::cout << refTables[i];
        }
        std::cout << std::endl;
        
        // Test check option
        view2.setCheckOption(true);
        view2.setCheckOptionType("CASCADED");
        
        std::cout << "Has check option: " << view2.hasCheckOption() << std::endl;
        std::cout << "Check option type: " << view2.checkOptionType() << std::endl;
        
        // Test SQL generation
        std::cout << "Qualified name: " << view2.qualifiedName() << std::endl;
        std::cout << "CREATE statement: " << view2.createStatement() << std::endl;
        std::cout << "DROP statement: " << view2.dropStatement() << std::endl;
        
        // Test materialized view
        SqlView materializedView = SqlView::createMaterialized(
            "user_stats", 
            "SELECT COUNT(*) as total_users, AVG(age) as avg_age FROM users"
        );
        
        std::cout << "Materialized view: " << materializedView.name() << std::endl;
        std::cout << "Is materialized: " << materializedView.isMaterialized() << std::endl;
        std::cout << "Definition: " << materializedView.definition() << std::endl;
        
        // Test updatable view
        SqlView updatableView = SqlView::createUpdatable(
            "user_details",
            "SELECT id, name, email FROM users"
        );
        
        std::cout << "Updatable view: " << updatableView.name() << std::endl;
        std::cout << "Is updatable: " << updatableView.isUpdatable() << std::endl;
        
        // Test copy constructor
        SqlView view3 = view2;
        std::cout << "Copied view name: " << view3.name() << std::endl;
        std::cout << "Copied view schema: " << view3.schema() << std::endl;
        std::cout << "Copied view definition: " << view3.definition() << std::endl;
        
        // Test move constructor
        SqlView view4 = std::move(view3);
        std::cout << "Moved view name: " << view4.name() << std::endl;
        std::cout << "Moved view updatable: " << view4.isUpdatable() << std::endl;
        
        // Test metadata object
        SqlViewMetadata metadata;
        metadata.schema = "test_schema";
        metadata.definition = "SELECT * FROM test_table";
        metadata.comment = "Test view metadata";
        metadata.isUpdatable = true;
        metadata.isMaterialized = false;
        metadata.withCheckOption = true;
        metadata.checkOption = "LOCAL";
        metadata.definer = "test_user@localhost";
        metadata.securityType = "INVOKER";
        
        SqlView view5("test_view", metadata);
        std::cout << "View from metadata - name: " << view5.name() << std::endl;
        std::cout << "View from metadata - schema: " << view5.schema() << std::endl;
        std::cout << "View from metadata - definition: " << view5.definition() << std::endl;
        std::cout << "View from metadata - comment: " << view5.comment() << std::endl;
        
        // Test definition validation
        SqlView validView("valid_view", "SELECT 1 as test_column");
        SqlView invalidView("invalid_view", "");
        
        std::cout << "Valid view definition valid: " << validView.isDefinitionValid() << std::endl;
        std::cout << "Invalid view definition valid: " << invalidView.isDefinitionValid() << std::endl;
        
        // Test hasMetadata
        std::cout << "View2 has metadata: " << view2.hasMetadata() << std::endl;
        std::cout << "View1 has metadata: " << view1.hasMetadata() << std::endl;
        
        // Test connection status (without actual database)
        std::cout << "View2 is connected: " << view2.isConnected() << std::endl;
        
        // Test factory method
        SqlView dbView = SqlView::fromDatabase(nullptr, "test_db_view", "public", false);
        std::cout << "DB view name: " << dbView.name() << std::endl;
        std::cout << "DB view schema: " << dbView.schema() << std::endl;
        
        std::cout << "All SqlView tests passed!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
