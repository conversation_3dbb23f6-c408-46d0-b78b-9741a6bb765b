#include "sql/sql_row.h"
#include "sql/sql_table.h"
#include "sql/sql_database.h"
#include <iostream>

using namespace database;

int main() {
    try {
        // Test basic row creation
        SqlRow row1("users");
        std::cout << "Created row for table: " << row1.tableName() << std::endl;
        
        // Test value setting and getting
        row1.setValue("id", Data(1));
        row1.setValue("name", Data("John Doe"));
        row1.setValue("email", Data("<EMAIL>"));
        row1.setValue("age", Data(30));
        
        std::cout << "Row values:" << std::endl;
        std::cout << "  id: " << row1.value("id").to<int>() << std::endl;
        std::cout << "  name: " << row1.value("name").to<std::string>() << std::endl;
        std::cout << "  email: " << row1.value("email").to<std::string>() << std::endl;
        std::cout << "  age: " << row1.value("age").to<int>() << std::endl;
        
        // Test column management
        std::cout << "Column count: " << row1.columnCount() << std::endl;
        std::cout << "Has 'name' column: " << row1.hasColumn("name") << std::endl;
        std::cout << "Has 'salary' column: " << row1.hasColumn("salary") << std::endl;
        
        auto columnNames = row1.columnNames();
        std::cout << "Column names: ";
        for (size_t i = 0; i < columnNames.size(); ++i) {
            if (i > 0) std::cout << ", ";
            std::cout << columnNames[i];
        }
        std::cout << std::endl;
        
        // Test operator[] access
        row1["salary"] = Data(50000.0);
        std::cout << "Salary (via operator[]): " << row1["salary"].to<double>() << std::endl;
        
        // Test state management
        std::cout << "Is dirty: " << row1.isDirty() << std::endl;
        std::cout << "Is deleted: " << row1.isDeleted() << std::endl;
        std::cout << "Is empty: " << row1.isEmpty() << std::endl;
        
        row1.setDirty(false);
        row1.setRowId(123);
        row1.setVersion("v1.0");
        
        std::cout << "Row ID: " << row1.rowId().value_or(0) << std::endl;
        std::cout << "Version: " << row1.version().value_or("none") << std::endl;
        
        // Test validation
        std::cout << "Is valid: " << row1.isValid() << std::endl;
        auto errors = row1.validationErrors();
        std::cout << "Validation errors: " << errors.size() << std::endl;
        
        // Test SQL generation
        std::cout << "Qualified name: " << row1.qualifiedName() << std::endl;
        std::cout << "INSERT SQL: " << row1.toInsertStatement() << std::endl;
        std::cout << "UPDATE SQL: " << row1.toUpdateStatement("id = ?") << std::endl;
        std::cout << "JSON: " << row1.toJson() << std::endl;
        
        // Test with DataMap constructor
        DataMap values = {
            {"id", Data(2)},
            {"name", Data("Jane Smith")},
            {"email", Data("<EMAIL>")}
        };
        
        SqlRow row2("users", values);
        std::cout << "Row2 name: " << row2.value("name").to<std::string>() << std::endl;
        std::cout << "Row2 column count: " << row2.columnCount() << std::endl;
        
        // Test copy constructor
        SqlRow row3 = row1;
        std::cout << "Copied row name: " << row3.value("name").to<std::string>() << std::endl;
        std::cout << "Copied row table: " << row3.tableName() << std::endl;
        
        // Test move constructor
        SqlRow row4 = std::move(row2);
        std::cout << "Moved row name: " << row4.value("name").to<std::string>() << std::endl;
        std::cout << "Moved row column count: " << row4.columnCount() << std::endl;
        
        // Test column removal
        row1.removeColumn("age");
        std::cout << "Column count after removal: " << row1.columnCount() << std::endl;
        std::cout << "Has 'age' column after removal: " << row1.hasColumn("age") << std::endl;
        
        // Test clear
        SqlRow row5("test");
        row5.setValue("col1", Data("value1"));
        row5.setValue("col2", Data("value2"));
        std::cout << "Before clear - column count: " << row5.columnCount() << std::endl;
        
        row5.clear();
        std::cout << "After clear - column count: " << row5.columnCount() << std::endl;
        std::cout << "After clear - is empty: " << row5.isEmpty() << std::endl;
        
        // Test table association
        SqlTable table("products");
        SqlRow row6("products");
        row6.setTable(table);
        
        auto associatedTable = row6.table();
        std::cout << "Associated table name: " << associatedTable.name() << std::endl;
        
        // Test index access
        row1.setValue("test_col", Data("test_value"));
        auto names = row1.columnNames();
        if (!names.empty()) {
            std::cout << "First column by index: " << row1.value(0).to<std::string>() << std::endl;
        }
        
        std::cout << "All SqlRow tests passed!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
