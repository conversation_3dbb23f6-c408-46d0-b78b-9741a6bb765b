#ifndef DATABASE_SQL_INDEX_H
#define DATABASE_SQL_INDEX_H

#include <string>
#include <string_view>
#include <vector>
#include <optional>
#include <chrono>
#include <unordered_map>

#include "sql_object.h"

namespace database {

// Forward declarations
class SqlIndexPrivate;
class SqlTable;
class SqlDatabase;

/**
 * @brief Represents a database index with lazy-loaded metadata
 * 
 * This class serves as the core representation of a database index,
 * providing both SQL building capabilities and metadata access through
 * lazy loading for optimal performance.
 */
class SqlIndex final : public SqlObject {
public:
    /**
     * @brief Simple index metadata structure
     */
    struct SqlIndexMetadata {
        std::string tableName;                              ///< Parent table name
        std::string comment;                                ///< Index comment/description
        SqlIndexType indexType = SqlIndexType::Unknown;    ///< Index type
        std::vector<std::string> columns;                   ///< Indexed columns
        std::vector<SqlSortOrder> sortOrders;               ///< Sort order for each column
        std::optional<std::string> whereClause;             ///< Partial index condition
        std::optional<size_t> fillfactor;                   ///< Fill factor percentage
        bool isUnique = false;                              ///< Is unique index
        bool isClustered = false;                           ///< Is clustered index
        bool isVisible = true;                              ///< Is visible to query optimizer
        bool isSystem = false;                              ///< Is system-generated index
        std::optional<std::chrono::system_clock::time_point> createdTime; ///< Creation time
        std::optional<size_t> pages;                        ///< Number of pages
        std::optional<size_t> rows;                         ///< Number of rows
        std::optional<double> selectivity;                  ///< Index selectivity
        std::unordered_map<std::string, std::string> properties; ///< Additional properties

        SqlIndexMetadata() = default;
        SqlIndexMetadata(std::string tableName, SqlIndexType indexType, std::vector<std::string> columns)
            : tableName(std::move(tableName)), indexType(indexType), columns(std::move(columns)) {}

        // std::string definition;
        // bool primary = false;
    };

    SqlIndex();
    explicit SqlIndex(std::string_view name, SqlIndexType indexType = SqlIndexType::Unknown);
    explicit SqlIndex(std::string_view name, const SqlTable& table);

    // Copy/move operations
    SqlIndex(const SqlIndex& other) = default;
    SqlIndex(SqlIndex&& other) noexcept = default;
    SqlIndex& operator=(const SqlIndex& other) = default;
    SqlIndex& operator=(SqlIndex&& other) noexcept = default;

    ~SqlIndex() = default;

    [[nodiscard]] static SqlIndex fromDatabase(std::shared_ptr<SqlTable> table,
                                               const std::string& indexName,
                                               bool loadMetadata = true);

    //----------------------------------------------------------------------
    // Table Metadata
    //----------------------------------------------------------------------

    [[nodiscard]] const SqlIndexMetadata& metadata() const noexcept;
    SqlTable& setMetadata(const SqlIndexMetadata& metadata);
    bool loadMetadata();
    bool refreshMetadata();

    [[nodiscard]] SqlIndexType indexType() const;
    void setIndexType(SqlIndexType indexType);
    [[nodiscard]] std::string tableName() const;
    void setTableName(std::string tableName);
    [[nodiscard]] std::string comment() const;
    void setComment(std::string comment);
    [[nodiscard]] std::vector<std::string> columns() const;
    void setColumns(std::vector<std::string> columns);
    void addColumn(std::string columnName, SqlSortOrder sortOrder = SqlSortOrder::Ascending);
    void removeColumn(const std::string& columnName);
    void clearColumns();
    [[nodiscard]] std::vector<SqlSortOrder> sortOrders() const;
    void setSortOrders(std::vector<SqlSortOrder> sortOrders);
    [[nodiscard]] std::optional<std::string> whereClause() const;
    void setWhereClause(std::optional<std::string> whereClause);
    [[nodiscard]] bool isUnique() const;
    void setUnique(bool unique);
    [[nodiscard]] bool isClustered() const;
    void setClustered(bool clustered);
    [[nodiscard]] bool isVisible() const;
    void setVisible(bool visible);
    [[nodiscard]] bool isSystem() const;
    void setSystem(bool system);
    [[nodiscard]] bool isPrimaryKey() const;
    [[nodiscard]] std::optional<size_t> fillFactor() const;
    void setFillFactor(std::optional<size_t> fillFactor);

    //----------------------------------------------------------------------
    // Database Interaction Operations
    //----------------------------------------------------------------------
    [[nodiscard]] SqlTable table() const noexcept;
    void setTable(const SqlTable& table);

    [[nodiscard]] std::shared_ptr<SqlDatabase> database() const;

    [[nodiscard]] bool exists() const;
    bool create(bool ifNotExists = true);
    bool drop(bool ifExists = true);
    bool rebuild();
    bool analyze();

    [[nodiscard]] size_t sizeBytes() const;
    [[nodiscard]] size_t pageCount() const;
    [[nodiscard]] size_t rowCount() const;
    [[nodiscard]] std::optional<double> selectivity() const;
    [[nodiscard]] bool isUsed() const;
    [[nodiscard]] std::unordered_map<std::string, std::string> usageStatistics() const;

    [[nodiscard]] std::string qualifiedName() const;
    [[nodiscard]] std::string toSql() const;

    //----------------------------------------------------------------------
    // Utility methods
    //----------------------------------------------------------------------
    [[nodiscard]] std::string createSql(bool ifNotExists = false) const;
    [[nodiscard]] std::string dropSql(bool ifExists = false) const;

private:
    mutable std::shared_ptr<SqlIndexMetadata> m_metadata;
    std::shared_ptr<SqlIndexPrivate> d_ptr;

    // Helper methods
    void ensureDatabase() const;
};

} // namespace database

#endif // DATABASE_SQL_INDEX_H 
