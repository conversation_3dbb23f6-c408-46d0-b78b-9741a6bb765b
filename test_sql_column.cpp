#include "sql/sql_column.h"
#include "sql/sql_table.h"
#include "sql/sql_condition.h"
#include <iostream>

using namespace database;

int main() {
    try {
        // Test basic column creation
        SqlColumn column1("id", SqlDataType::Integer);
        std::cout << "Created column: " << column1.name() << std::endl;
        std::cout << "Data type: " << static_cast<int>(column1.dataType()) << std::endl;
        
        // Test metadata
        column1.setNullable(false);
        column1.setPrimaryKey(true);
        column1.setAutoIncrement(true);
        
        std::cout << "Is nullable: " << column1.isNullable() << std::endl;
        std::cout << "Is primary key: " << column1.isPrimaryKey() << std::endl;
        std::cout << "Is auto increment: " << column1.isAutoIncrement() << std::endl;
        
        // Test SQL generation
        std::cout << "Definition SQL: " << column1.definitionSql() << std::endl;
        std::cout << "Qualified name: " << column1.qualifiedName() << std::endl;
        
        // Test with table
        SqlTable table("users");
        SqlColumn column2("name", table);
        column2.setDataType(SqlDataType::Varchar);
        column2.setMaxLength(255);
        column2.setNullable(false);
        
        std::cout << "Column 2 table name: " << column2.tableName() << std::endl;
        std::cout << "Column 2 definition: " << column2.definitionSql() << std::endl;
        
        // Test conditions (these are private methods, so we can't test them directly)
        // But we can test that the column compiles correctly
        
        std::cout << "All tests passed!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
