#ifndef DATABASE_SQL_ROW_H
#define DATABASE_SQL_ROW_H

#include <string>
#include <string_view>
#include <unordered_map>
#include <vector>
#include <chrono>
#include <optional>

#include "sql_column.h"

namespace database {

// Forward declarations
class SqlRowPrivate;

/**
 * @brief Class representing a database row
 *
 * This class encapsulates a database row with column values.
 * It provides methods for setting and getting column values.
 */
class SqlRow final : public SqlObject {
public:
    /**
     * @brief Metadata for SQL row objects (Tier 2 - Extended Metadata)
     *
     * This struct contains comprehensive descriptive information about table rows.
     */
    struct SqlRowMetadata {
        std::string tableName;                              ///< Parent table name
        std::vector<std::string> columnNames;               ///< Column names in order
        std::optional<std::chrono::system_clock::time_point> insertTime; ///< Insert timestamp
        std::optional<std::chrono::system_clock::time_point> updateTime; ///< Last update timestamp
        std::optional<std::string> version;                 ///< Row version for optimistic locking
        std::optional<size_t> rowId;                        ///< Physical row identifier
        bool isDeleted = false;                             ///< Soft delete flag
        bool isDirty = false;                               ///< Has unsaved changes
        std::unordered_map<std::string, std::string> properties; ///< Additional properties

    /**
     * @brief Default constructor
     */
        SqlRowMetadata() = default;

    /**
     * @brief Constructor with basic information
     */
        SqlRowMetadata(std::string tableName, std::vector<std::string> columnNames)
            : tableName(std::move(tableName)), columnNames(std::move(columnNames)) {}
    };

    SqlRow() = default;
    explicit SqlRow(std::string_view tableName);
    explicit SqlRow(std::string_view tableName, DataMap values);
    explicit SqlRow(std::string_view tableName, DataMap&& values) noexcept;

    // Copy/move operations
    SqlRow(const SqlRow& other);
    SqlRow& operator=(const SqlRow& other);
    SqlRow(SqlRow&& other) noexcept;
    SqlRow& operator=(SqlRow&& other) noexcept;

    //----------------------------------------------------------------------
    // Static Methods
    //----------------------------------------------------------------------
    [[nodiscard]] static SqlRow fromDatabase(std::shared_ptr<SqlTable> table,
                                             const std::string& whereClause,
                                             bool loadMetadata = true);

    //----------------------------------------------------------------------
    // Row Metadata
    //----------------------------------------------------------------------
    [[nodiscard]] std::shared_ptr<SqlRowMetadata> metadata() const;
    void setMetadata(const SqlRowMetadata& metadata);

    [[nodiscard]] Data value(const std::string& columnName) const;
    [[nodiscard]] Data value(size_t index) const;
    void setValue(const std::string& columnName, Data value);
    void setValue(size_t index, Data value);
    [[nodiscard]] const DataMap& values() const;
    void setValues(DataMap values);

    [[nodiscard]] bool hasColumn(const std::string& columnName) const;
    [[nodiscard]] size_t columnCount() const;

    [[nodiscard]] std::vector<std::string> columnNames() const;
    void removeColumn(const std::string& columnName);

    [[nodiscard]] bool isEmpty() const;
    void clear();

    [[nodiscard]] bool isDirty() const;
    void setDirty(bool dirty);
    [[nodiscard]] bool isDeleted() const;
    void setDeleted(bool deleted);
    [[nodiscard]] std::optional<size_t> rowId() const;
    void setRowId(std::optional<size_t> rowId);
    [[nodiscard]] std::optional<std::string> version() const;
    void setVersion(std::optional<std::string> version);


    [[nodiscard]] std::string tableName() const;
    void setTableName(std::string tableName);

    Data& operator[](const std::string& columnName);
    const Data& operator[](const std::string& columnName) const;
    Data& operator[](size_t index);
    const Data& operator[](size_t index) const;

    //----------------------------------------------------------------------
    // Table Association
    //----------------------------------------------------------------------
    [[nodiscard]] SqlTable table() const noexcept;
    void setTable(const SqlTable& table) noexcept;

    [[nodiscard]] std::shared_ptr<SqlDatabase> database() const;
    bool save();
    bool insert();
    bool update(const std::string& whereClause = {});
    bool deleteFromDatabase(const std::string& whereClause = {});
    bool reload(const std::string& whereClause = {});
    [[nodiscard]] bool exists(const std::string& whereClause = {}) const;
    bool refreshMetadata();
    [[nodiscard]] bool isValid() const;
    [[nodiscard]] std::vector<std::string> validationErrors() const;
    [[nodiscard]] bool isColumnValid(const std::string& columnName) const;

    [[nodiscard]] std::string qualifiedName() const override;
    [[nodiscard]] std::string toSql() const override;
    [[nodiscard]] std::string toJson() const;

    [[nodiscard]] std::string toInsertStatement(const std::string& tableName = {}) const;
    [[nodiscard]] std::string toUpdateStatement(const std::string& whereClause,
                                                const std::string& tableName = {}) const;

private:
    DataMap m_values;
    std::shared_ptr<SqlRowMetadata> m_metadata;
    std::shared_ptr<SqlRowPrivate> d_ptr;

    // Helper methods for tier management
    void ensureDatabase() const;

    // Internal helpers
    void updateColumnNames();
    Data& getValueRef(const std::string& columnName);
    const Data& getValueRef(const std::string& columnName) const;
    Data& getValueRef(size_t index);
    const Data& getValueRef(size_t index) const;
};

} // namespace database

#endif // DATABASE_SQL_ROW_H
